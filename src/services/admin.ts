import { useQuery } from "@tanstack/react-query";
import { User } from "@/types/auth";
import { CoursePermissionItem } from "@/types/courses";
import { API_URL, Pagination, req } from "@/utils/api";
import { qs } from "@/utils/qs";
import {
  COURSE_PERMISSION,
  Department,
  GroupPermission,
  Organization,
  PERMISSION,
} from "./../types/organization";

// group
export const getGroups = () => {
  return req<GroupPermission[]>(`${API_URL}/api/v1/admin/groups`, {
    withAuth: true,
  });
};

export const useGroupsPermissions = () => {
  return useQuery({
    queryKey: ["admin-groups"],
    queryFn: async () => {
      try {
        const groups = await getGroups();
        return groups || [];
      } catch (error) {
        console.error("Error fetching groups:", error);
        return [];
      }
    },
  });
};

export const createGroup = (params: Omit<GroupPermission, "id">) => {
  return req(`${API_URL}/api/v1/admin/groups`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const updateGroup = (
  params: Omit<GroupPermission, "users" | "name">
) => {
  return req(`${API_URL}/api/v1/admin/groups/${params.id}`, {
    method: "PATCH",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const deleteGroup = (groupId: string) => {
  return req(`${API_URL}/api/v1/admin/groups/${groupId}`, {
    method: "DELETE",
    withAuth: true,
  });
};

export const addMembersToGroup = (params: {
  group_id: string;
  user_ids: string[];
}) => {
  return req(`${API_URL}/api/v1/admin/groups/${params.group_id}/members`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params.user_ids),
  });
};

export const updateGroupPermissionMembers = (params: {
  group_id: string;
  added_users: string[];
  removed_users: string[];
}) => {
  return req(`${API_URL}/api/v1/admin/groups/${params.group_id}/members`, {
    method: "PATCH",
    withAuth: true,
    body: JSON.stringify({
      added_users: params.added_users,
      removed_users: params.removed_users,
    }),
  });
};

export const getGroupsPermissionMembers = (groupId: string) => {
  return req<User[]>(`${API_URL}/api/v1/admin/groups/${groupId}/members`, {
    withAuth: true,
  });
};

// department

export const getDepartments = () => {
  return req<Department[]>(`${API_URL}/api/v1/admin/departments`, {
    withAuth: true,
  });
};

export const useDepartments = () => {
  return useQuery({
    queryKey: ["admin-departments"],
    queryFn: async () => {
      try {
        const departments = await getDepartments();
        return departments || [];
      } catch (error) {
        console.error("Error fetching departments:", error);
        return [];
      }
    },
  });
};

export const createDepartment = (params: Omit<Department, "id" | "users">) => {
  return req(`${API_URL}/api/v1/admin/departments`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const updateDepartment = (
  params: Omit<Department, "users" | "name">
) => {
  return req(`${API_URL}/api/v1/admin/departments/${params.id}`, {
    method: "PATCH",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const deleteDepartment = (departmentId: string) => {
  return req(`${API_URL}/api/v1/admin/departments/${departmentId}`, {
    method: "DELETE",
    withAuth: true,
  });
};

export const addMembersToDepartment = (params: {
  department_id: string;
  user_ids: string[];
}) => {
  return req(
    `${API_URL}/api/v1/admin/departments/${params.department_id}/members`,
    {
      method: "POST",
      withAuth: true,
      body: JSON.stringify(params.user_ids),
    }
  );
};

export const updateDepartmentMembers = (params: {
  department_id: string;
  added_users: string[];
  removed_users: string[];
}) => {
  return req(
    `${API_URL}/api/v1/admin/departments/${params.department_id}/members`,
    {
      method: "PATCH",
      withAuth: true,
      body: JSON.stringify({
        added_users: params.added_users,
        removed_users: params.removed_users,
      }),
    }
  );
};

export const getDepartmentMembers = (departmentId: string) => {
  return req<User[]>(
    `${API_URL}/api/v1/admin/departments/${departmentId}/members`,
    {
      withAuth: true,
    }
  );
};

// user

export const importUsers = (params: { name: string; email: string }[]) => {
  return req(`${API_URL}/api/v1/admin/users/import`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const getUsers = async (
  params?: {
    search: string;
  } & Omit<Pagination, "page" | "limit">
): Promise<{ data: User[]; metadata: Pagination }> => {
  const queryString = params ? qs(params) : "";
  return req<User[], Pagination>(
    `${API_URL}/api/v1/admin/users${queryString}`,
    {
      withAuth: true,
      withMetadata: true,
    }
  );
};

export const useUsers = (
  params?: { search: string } & Omit<Pagination, "page" | "limit">
) => {
  return useQuery({
    queryKey: ["admin-users", params?.search],
    queryFn: async () => {
      try {
        return getUsers(params);
      } catch (error) {
        console.error("Error fetching users:", error);
        return { data: [], metadata: { page: 1, limit: 0, total: 0 } };
      }
    },
    initialData: { data: [], metadata: { page: 1, limit: 0, total: 0 } },
  });
};

export const deleteUser = (userIds: string[]) => {
  return req(`${API_URL}/api/v1/admin/users`, {
    method: "DELETE",
    withAuth: true,
    body: JSON.stringify(userIds),
  });
};

export const createUser = (params: { name: string; email: string }) => {
  return req<User[]>(`${API_URL}/api/v1/admin/users/import`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify([params]), // Wrap in array since import endpoint expects array
  });
};

// course

export const getCoursePermissions = (
  courseId: string,
  target: "user" | "department"
): Promise<CoursePermissionItem[]> => {
  return req(
    `${API_URL}/api/v1/instructors/courses/${courseId}/permissions${qs({
      target,
    })}`,
    {
      withAuth: true,
    }
  );
};

export const useCoursePermissions = (
  courseId: string,
  target: "user" | "department"
) => {
  return useQuery({
    queryKey: ["course-permissions", courseId, target],
    queryFn: () => getCoursePermissions(courseId, target),
    enabled: !!courseId,
    initialData: [],
  });
};

export const addCoursePermission = (
  params: Array<{
    course_id: string;
    department_id?: string;
    user_id?: string;
  }>
) => {
  return req(`${API_URL}/api/v1/instructors/courses/permissions/add`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const removeCoursePermission = (
  params: Array<{
    course_id: string;
    department_id?: string;
    user_id?: string;
  }>
) => {
  return req(`${API_URL}/api/v1/instructors/courses/permissions/remove`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

// organization
export const getOrganization = () => {
  return req<Organization>(`${API_URL}/api/v1/organization`, {
    withAuth: true,
  });
};

export const useOrganization = () => {
  return useQuery({
    queryKey: ["organization"],
    queryFn: getOrganization,
    staleTime: 5 * 1000,
  });
};

export const createOrganization = (params: { name: string }) => {
  return req<Organization>(`${API_URL}/api/v1/admin/organization`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const updateOrganization = (params: Partial<Organization>) => {
  return req<Organization>(`${API_URL}/api/v1/admin/organization`, {
    method: "PATCH",
    withAuth: true,
    body: JSON.stringify(params),
  });
};
