import { useQueryClient } from "@tanstack/react-query";
import { Users } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { DepartmentSelector } from "@/components/DepartmentSelector";
import { UserSelector } from "@/components/UserSelector";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import {
  addCoursePermission,
  removeCoursePermission,
  useCoursePermissions,
  useDepartments,
  useUsers,
} from "@/services/admin";
import { CoursePermissionItem } from "@/types/courses";

interface PermissionSelectorProps {
  courseId: string;
  triggerButton?: React.ReactNode;
  onSuccess?: () => void;
}

export function PermissionSelector({
  courseId,
  triggerButton,
  onSuccess,
}: PermissionSelectorProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [userSearchTerm, setUserSearchTerm] = useState("");

  const queryClient = useQueryClient();
  const { data: users = [] } = useUsers();
  const { data: departments = [] } = useDepartments();

  // Load existing permissions to show current selections
  const { data: existingDepartmentPermissions = [] } = useCoursePermissions(
    courseId,
    "department",
  );
  const { data: existingUserPermissions = [] } = useCoursePermissions(
    courseId,
    "user",
  );

  // Load current selections when dialog opens
  useEffect(() => {
    if (isDialogOpen) {
      // Load current department permissions
      const currentDepartmentIds = Array.isArray(existingDepartmentPermissions)
        ? existingDepartmentPermissions
            .map((p) => p.department_id)
            .filter((id): id is string => Boolean(id))
        : [];
      setSelectedDepartments(currentDepartmentIds);

      // Load current user permissions
      const currentUserIds = Array.isArray(existingUserPermissions)
        ? existingUserPermissions
            .map((p) => p.user_id)
            .filter((id): id is string => Boolean(id))
        : [];
      setSelectedUsers(currentUserIds);
    }
  }, [isDialogOpen, existingDepartmentPermissions, existingUserPermissions]);

  const handleSavePermissions = async () => {
    if (!courseId) return;

    try {
      // Get current permissions
      const currentDepartmentIds = Array.isArray(existingDepartmentPermissions)
        ? existingDepartmentPermissions
            .map((p) => p.department_id)
            .filter((id): id is string => Boolean(id))
        : [];
      const currentUserIds = Array.isArray(existingUserPermissions)
        ? existingUserPermissions
            .map((p) => p.user_id)
            .filter((id): id is string => Boolean(id))
        : [];

      // Find permissions to add and remove
      const departmentsToAdd = selectedDepartments.filter(
        (id) => !currentDepartmentIds.includes(id),
      );
      const departmentsToRemove = currentDepartmentIds.filter(
        (id) => !selectedDepartments.includes(id),
      );
      const usersToAdd = selectedUsers.filter(
        (id) => !currentUserIds.includes(id),
      );
      const usersToRemove = currentUserIds.filter(
        (id) => !selectedUsers.includes(id),
      );

      // Remove permissions that are no longer selected
      const permissionsToRemove: CoursePermissionItem[] = [];
      departmentsToRemove.forEach((departmentId) => {
        permissionsToRemove.push({
          course_id: courseId,
          department_id: departmentId,
        });
      });
      usersToRemove.forEach((userId) => {
        permissionsToRemove.push({
          course_id: courseId,
          user_id: userId,
        });
      });

      if (permissionsToRemove.length > 0) {
        await removeCoursePermission(permissionsToRemove);
      }

      // Add new permissions
      const permissionsToAdd: CoursePermissionItem[] = [];
      departmentsToAdd.forEach((departmentId) => {
        permissionsToAdd.push({
          course_id: courseId,
          department_id: departmentId,
        });
      });
      usersToAdd.forEach((userId) => {
        permissionsToAdd.push({
          course_id: courseId,
          user_id: userId,
        });
      });

      if (permissionsToAdd.length > 0) {
        await addCoursePermission(permissionsToAdd);
      }

      // Show success toast
      toast.success("Cập nhật quyền truy cập thành công!");

      // Invalidate and refetch permissions data
      await queryClient.invalidateQueries({
        queryKey: ["course-permissions", courseId, "department"],
      });
      await queryClient.invalidateQueries({
        queryKey: ["course-permissions", courseId, "user"],
      });

      // Reset state and close dialog
      setIsDialogOpen(false);
      setSelectedDepartments([]);
      setSelectedUsers([]);

      // Call success callback
      onSuccess?.();
    } catch (error) {
      console.error("Error updating permissions:", error);
      toast.error("Có lỗi xảy ra khi cập nhật quyền truy cập!");
    }
  };

  const defaultTriggerButton = (
    <Button className="gap-2">
      <Users className="h-4 w-4" />
      Thêm quyền truy cập
    </Button>
  );

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        {triggerButton || defaultTriggerButton}
      </DialogTrigger>
      <DialogContent className="sm:!max-w-4xl overflow-y-auto">
        <DialogHeader className="h-max">
          <DialogTitle className="font-semibold text-xl">
            Phân quyền truy cập khóa học
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Chọn phòng ban hoặc người dùng để cấp quyền truy cập khóa học này
          </DialogDescription>
        </DialogHeader>
        <div className="mt-6 min-h-[50dvh]">
          {/* Department Selection */}
          <div className="grid gap-8 md:grid-cols-1">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <svg
                  className="h-4 w-4 text-gray-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                  />
                </svg>
                <label className="font-medium text-gray-700 text-sm">
                  Phòng ban
                </label>
                <p className="text-gray-500 text-xs">
                  Chọn một hoặc nhiều phòng ban cần cấp quyền
                </p>
              </div>
              <DepartmentSelector
                departments={departments.map((d) => ({
                  id: d.id,
                  name: d.name,
                  description: d.description,
                  memberCount: d.users || 0,
                }))}
                selectedDepartments={selectedDepartments}
                onSelectionChange={setSelectedDepartments}
                placeholder="Chọn phòng ban..."
              />
            </div>

            {/* User Selection */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <svg
                  className="h-4 w-4 text-gray-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
                <label className="font-medium text-gray-700 text-sm">
                  Thành viên
                </label>
                <p className="text-gray-500 text-xs">
                  Tìm kiếm và chọn thành viên cần cấp quyền
                </p>
              </div>
              <UserSelector
                users={users.map((u) => ({
                  id: u.id,
                  name: u.name,
                  email: u.email,
                  avatar: u.avatar_url,
                  department: u.departments?.[0]?.department?.name || "Unknown",
                }))}
                selectedUsers={selectedUsers}
                onSelectionChange={setSelectedUsers}
                searchValue={userSearchTerm}
                onSearchChange={setUserSearchTerm}
                isLoading={false}
                placeholder="Tìm kiếm theo tên hoặc email..."
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button
            variant="outline"
            onClick={() => setIsDialogOpen(false)}
            className="shadow-sm"
          >
            Hủy
          </Button>
          <Button
            onClick={handleSavePermissions}
            disabled={
              selectedDepartments.length === 0 && selectedUsers.length === 0
            }
          >
            Lưu quyền truy cập
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Component content without dialog wrapper - for use in forms
export function PermissionSelectorContent({
  courseId,
  onPermissionsChange,
}: {
  courseId: string;
  onPermissionsChange?: () => void;
}) {
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [userSearchTerm, setUserSearchTerm] = useState("");

  const queryClient = useQueryClient();
  const { data: users = [] } = useUsers();
  const { data: departments = [] } = useDepartments();

  // Load existing permissions to show current selections
  const { data: existingDepartmentPermissions = [] } = useCoursePermissions(
    courseId,
    "department",
  );
  const { data: existingUserPermissions = [] } = useCoursePermissions(
    courseId,
    "user",
  );

  // Load current selections when component mounts or data changes
  useEffect(() => {
    // Load current department permissions
    const currentDepartmentIds = Array.isArray(existingDepartmentPermissions)
      ? existingDepartmentPermissions
          .map((p) => p.department_id)
          .filter((id): id is string => Boolean(id))
      : [];
    setSelectedDepartments(currentDepartmentIds);

    // Load current user permissions
    const currentUserIds = Array.isArray(existingUserPermissions)
      ? existingUserPermissions
          .map((p) => p.user_id)
          .filter((id): id is string => Boolean(id))
      : [];
    setSelectedUsers(currentUserIds);
  }, [existingDepartmentPermissions, existingUserPermissions]);

  const handleSavePermissions = async () => {
    if (!courseId) return;

    try {
      // Get current permissions
      const currentDepartmentIds = Array.isArray(existingDepartmentPermissions)
        ? existingDepartmentPermissions
            .map((p) => p.department_id)
            .filter((id): id is string => Boolean(id))
        : [];
      const currentUserIds = Array.isArray(existingUserPermissions)
        ? existingUserPermissions
            .map((p) => p.user_id)
            .filter((id): id is string => Boolean(id))
        : [];

      // Find permissions to add and remove
      const departmentsToAdd = selectedDepartments.filter(
        (id) => !currentDepartmentIds.includes(id),
      );
      const departmentsToRemove = currentDepartmentIds.filter(
        (id) => !selectedDepartments.includes(id),
      );
      const usersToAdd = selectedUsers.filter(
        (id) => !currentUserIds.includes(id),
      );
      const usersToRemove = currentUserIds.filter(
        (id) => !selectedUsers.includes(id),
      );

      // Remove permissions that are no longer selected
      const permissionsToRemove: CoursePermissionItem[] = [];
      departmentsToRemove.forEach((departmentId) => {
        permissionsToRemove.push({
          course_id: courseId,
          department_id: departmentId,
        });
      });
      usersToRemove.forEach((userId) => {
        permissionsToRemove.push({
          course_id: courseId,
          user_id: userId,
        });
      });

      if (permissionsToRemove.length > 0) {
        await removeCoursePermission(permissionsToRemove);
      }

      // Add new permissions
      const permissionsToAdd: CoursePermissionItem[] = [];
      departmentsToAdd.forEach((departmentId) => {
        permissionsToAdd.push({
          course_id: courseId,
          department_id: departmentId,
        });
      });
      usersToAdd.forEach((userId) => {
        permissionsToAdd.push({
          course_id: courseId,
          user_id: userId,
        });
      });

      if (permissionsToAdd.length > 0) {
        await addCoursePermission(permissionsToAdd);
      }

      // Show success toast
      toast.success("Cập nhật quyền truy cập thành công!");

      // Invalidate and refetch permissions data
      await queryClient.invalidateQueries({
        queryKey: ["course-permissions", courseId, "department"],
      });
      await queryClient.invalidateQueries({
        queryKey: ["course-permissions", courseId, "user"],
      });

      // Notify parent component
      onPermissionsChange?.();
    } catch (error) {
      console.error("Error updating permissions:", error);
      toast.error("Có lỗi xảy ra khi cập nhật quyền truy cập!");
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        {/* Department Selection */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <svg
              className="h-4 w-4 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
              />
            </svg>
            <label className="font-medium text-gray-700 text-sm">
              Phòng ban
            </label>
          </div>
          <DepartmentSelector
            departments={departments.map((d) => ({
              id: d.id,
              name: d.name,
              description: d.description,
              memberCount: d.users || 0,
            }))}
            selectedDepartments={selectedDepartments}
            onSelectionChange={setSelectedDepartments}
            placeholder="Chọn phòng ban..."
          />
          <p className="text-gray-500 text-xs">
            Chọn một hoặc nhiều phòng ban cần cấp quyền
          </p>
        </div>

        {/* User Selection */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <svg
              className="h-4 w-4 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
            <label className="font-medium text-gray-700 text-sm">
              Thành viên
            </label>
          </div>
          <UserSelector
            users={users.map((u) => ({
              id: u.id,
              name: u.name,
              email: u.email,
              avatar: u.avatar_url,
              department: u.departments?.[0]?.department?.name || "Unknown",
            }))}
            selectedUsers={selectedUsers}
            onSelectionChange={setSelectedUsers}
            searchValue={userSearchTerm}
            onSearchChange={setUserSearchTerm}
            isLoading={false}
            placeholder="Tìm kiếm theo tên hoặc email..."
          />
          <p className="text-gray-500 text-xs">
            Tìm kiếm và chọn thành viên cần cấp quyền
          </p>
        </div>
      </div>

      <div className="flex justify-end gap-3 pt-4">
        <Button
          onClick={handleSavePermissions}
          disabled={
            selectedDepartments.length === 0 && selectedUsers.length === 0
          }
          className="bg-blue-600 shadow-sm hover:bg-blue-700"
        >
          Lưu quyền truy cập
        </Button>
      </div>
    </div>
  );
}
