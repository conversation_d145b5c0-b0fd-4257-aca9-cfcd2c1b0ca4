export interface CourseData {
  id: number;
  title: string;
  slug: string;
  description: string;
  category: string;
  level: string;
  goal: string;
  imageUrl: string;
  status: "published" | "draft";
  duration: string;
  lessons: number;
  enrolled: number;
  completed: number;
  createdAt: string;
  updatedAt: string;
  instructor: string;
  completionRate: number;
  averageScore: number;
  requirement: "company" | "department" | "optional";
}

export interface Lesson {
  id: number;
  title: string;
  type: string;
  duration: string;
  status: string;
  module: number;
  slug: string;
  sections: number;
  contentType: "video" | "text" | "quiz" | "game";
}

export interface Student {
  id: number;
  name: string;
  department: string;
  enrolledDate: string;
  progress: number;
  status: string;
  score: number;
  studyTime: string;
  lastAccess: string;
  role: string;
  [key: string]: string | number | boolean | Date | null | undefined;
}

export interface HistoryItem {
  id: number;
  action: string;
  type: "create" | "edit" | "publish" | "permission";
  description: string;
  user: string;
  role: string;
  date: string;
  time: string;
}

export interface Department {
  id: number;
  name: string;
  userCount: number;
}

export interface User {
  id: number;
  name: string;
  email: string;
  department: string;
}

export interface Permission {
  id: string;
  department?: string;
  name?: string;
  email?: string;
  hasAccess: boolean;
  enrolledCount?: number;
}
