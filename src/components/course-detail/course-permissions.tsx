import { useQueryClient } from "@tanstack/react-query";
import { Plus, Users } from "lucide-react";
import { toast } from "sonner";
import { PermissionSelector } from "@/components/PermissionSelector";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { removeCoursePermission, useCoursePermissions } from "@/services/admin";
import { CoursePermissionItem } from "@/types/courses";
import type { Permission } from "./types";

interface CoursePermissionsProps {
  courseId: string;
}

export function CoursePermissions({ courseId }: CoursePermissionsProps) {
  const queryClient = useQueryClient();

  // Load existing permissions only
  const { data: existingDepartmentPermissions } = useCoursePermissions(
    courseId,
    "department",
  );
  const { data: existingUserPermissions } = useCoursePermissions(
    courseId,
    "user",
  );

  // Convert existing permissions to display format
  const finalDepartmentPermissions: Permission[] = Array.isArray(
    existingDepartmentPermissions,
  )
    ? existingDepartmentPermissions.map((perm) => ({
        id: perm.department_id || "0",
        department: perm.department?.name || `Department ${perm.department_id}`,
        hasAccess: true, // All existing permissions have access
        enrolledCount: perm.department?.total_member, // Mock enrolled count for now
      }))
    : [];

  const finalIndividualPermissions: Permission[] = Array.isArray(
    existingUserPermissions,
  )
    ? existingUserPermissions.map((perm) => ({
        id: perm.user_id || "0",
        name: perm.user?.name || `User ${perm.user_id}`,
        email: perm.user?.email || "",
        hasAccess: true, // All existing permissions have access
      }))
    : [];

  // Handle revoking individual permission
  const handleRevokePermission = async (
    type: "department" | "user",
    id: string,
  ) => {
    if (!courseId) return;

    try {
      const permissionToRemove: CoursePermissionItem[] = [
        {
          course_id: courseId,
          ...(type === "department" ? { department_id: id } : { user_id: id }),
        },
      ];

      await removeCoursePermission(permissionToRemove);

      // Show success toast
      toast.success("Thu hồi quyền thành công!");

      // Invalidate and refetch permissions data
      await queryClient.invalidateQueries({
        queryKey: ["course-permissions", courseId, "department"],
      });
      await queryClient.invalidateQueries({
        queryKey: ["course-permissions", courseId, "user"],
      });
    } catch (error) {
      console.error("Error revoking permission:", error);
      toast.error("Có lỗi xảy ra khi thu hồi quyền!");
    }
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="border-gray-100 border-b">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="font-semibold text-lg">
              Quản lý phân quyền truy cập
            </CardTitle>
            <CardDescription className="mt-1">
              Phân quyền truy cập khóa học theo phòng ban hoặc người dùng cụ thể
            </CardDescription>
          </div>
          <PermissionSelector
            courseId={courseId || ""}
            triggerButton={
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Thêm quyền truy cập
              </Button>
            }
          />
        </div>
      </CardHeader>
      <CardContent className="space-y-8 p-6">
        {/* Department Permissions */}
        <div>
          <h3 className="mb-4 font-semibold text-gray-900 text-lg">
            Quyền truy cập theo phòng ban
          </h3>
          <div className="overflow-hidden rounded-lg border border-gray-200 bg-white">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Phòng ban
                  </th>
                  <th className="px-6 py-4 text-left font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Trạng thái truy cập
                  </th>
                  <th className="px-6 py-4 text-left font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Số học viên đã đăng ký
                  </th>
                  <th className="px-6 py-4 text-left font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Thao tác
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {finalDepartmentPermissions.map((perm) => (
                  <tr
                    key={perm.id}
                    className="transition-colors hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 font-semibold text-gray-900">
                      {perm.department}
                    </td>
                    <td className="px-6 py-4">
                      <Badge
                        className="border-green-200 bg-green-100 text-green-800 shadow-sm"
                        variant="outline"
                      >
                        Có quyền truy cập
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-900 text-sm">
                          {perm.enrolledCount} học viên
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-red-200 text-red-700 shadow-sm hover:bg-red-50"
                        onClick={() =>
                          handleRevokePermission("department", perm.id)
                        }
                      >
                        Thu hồi quyền
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Individual Permissions */}
        <div>
          <h3 className="mb-4 font-semibold text-gray-900 text-lg">
            Quyền truy cập cá nhân
          </h3>
          <div className="space-y-3">
            {finalIndividualPermissions.map((perm) => (
              <div
                key={perm.id}
                className="flex items-center justify-between rounded-lg border border-gray-200 p-4 transition-colors hover:bg-gray-50"
              >
                <div className="flex items-center gap-4">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={"/placeholder.svg"} alt={perm.name} />
                    <AvatarFallback>{perm?.name?.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium text-gray-900">{perm.name}</div>
                    <div className="text-gray-600 text-sm">{perm.email}</div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Badge
                    className="border-green-200 bg-green-100 text-green-800 shadow-sm"
                    variant="outline"
                  >
                    Có quyền truy cập
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-red-200 text-red-700 shadow-sm hover:bg-red-50"
                    onClick={() => handleRevokePermission("user", perm.id)}
                  >
                    Thu hồi quyền
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
