import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import {
  BookOpen,
  Edit,
  Eye,
  FileText,
  GraduationCap,
  Grid3X3,
  List,
  Plus,
  Presentation,
  Timer,
  Trash2,
  Trophy,
  Video,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { LessonForm } from "@/components/lesson-editor/lesson-form";
import { LessonStatusBadge } from "@/components/lesson-editor/lesson-status-badge";
import { ModuleBadge } from "@/components/lesson-editor/module-badge";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CardContent } from "@/components/ui/card";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { useProfile } from "@/services/auth";
import { deleteLesson, useLessons } from "@/services/instructor";
import type { Course } from "@/types/courses";
import type { Lesson, LessonGroup } from "@/types/lessons";

interface CourseContentProps {
  course: Course;
}

export const CourseContent = ({ course }: CourseContentProps) => {
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<"list" | "cards">("cards");
  const [showLessonForm, setShowLessonForm] = useState(false);
  const [editingLesson, setEditingLesson] = useState<Lesson | undefined>();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [lessonToDelete, setLessonToDelete] = useState<Lesson | undefined>();
  const { data: lessons, refetch: refetchLessons } = useQuery(
    useLessons({ course_id: course.id }),
  );
  const { data: currentUser } = useProfile();

  const canEdit = currentUser?.id === course.instructor.id;

  // Group lessons by group field
  const groupLessons = (lessons: Lesson[]): LessonGroup[] => {
    const grouped: { [key: string]: Lesson[] } = {};
    const ungrouped: Lesson[] = [];

    lessons?.forEach((lesson) => {
      if (lesson.group && lesson.group.trim() !== "") {
        if (!grouped[lesson.group]) {
          grouped[lesson.group] = [];
        }
        grouped[lesson.group].push(lesson);
      } else {
        ungrouped.push(lesson);
      }
    });

    Object.keys(grouped).forEach((groupName) => {
      grouped[groupName].sort((a, b) => a.ordinal_index - b.ordinal_index);
    });
    ungrouped.sort((a, b) => a.ordinal_index - b.ordinal_index);

    const groups: LessonGroup[] = [];

    Object.keys(grouped)
      .sort() // Sort group names alphabetically
      .forEach((groupName) => {
        groups.push({
          groupName,
          lessons: grouped[groupName],
          isUngrouped: false,
        });
      });

    if (ungrouped.length > 0) {
      groups.push({
        groupName: "Khác",
        lessons: ungrouped,
        isUngrouped: true,
      });
    }

    return groups;
  };

  const lessonGroups = groupLessons(lessons || []);

  // Extract existing groups for LessonForm
  const existingGroups =
    lessons
      ?.filter((lesson) => lesson.group && lesson.group.trim() !== "")
      .map((lesson) => lesson.group!)
      .filter((group, index, arr) => arr.indexOf(group) === index) // unique groups
      .sort() || [];

  const handleEditLesson = (lesson: Lesson) => {
    setEditingLesson(lesson);
    setShowLessonForm(true);
  };

  const handleCreateLesson = () => {
    setEditingLesson(undefined);
    setShowLessonForm(true);
  };

  const handleCloseLessonForm = () => {
    setShowLessonForm(false);
    setEditingLesson(undefined);
  };

  const handleCreateSlide = (lesson: Lesson) => {
    navigate({
      to: "/dashboard/courses/$courseSlug/$lessonSlug",
      params: { lessonSlug: lesson.slug, courseSlug: course.slug },
    });
  };

  const handleDeleteLesson = (lesson: Lesson) => {
    setLessonToDelete(lesson);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteLesson = async () => {
    if (!lessonToDelete) return;

    try {
      await deleteLesson({ id: lessonToDelete.id });
      toast.success("Bài học đã được xóa thành công");
      refetchLessons();
    } catch (error) {
      toast.error(`Không thể xóa bài học: ${error.toString()}`);
    } finally {
      setShowDeleteConfirm(false);
      setLessonToDelete(undefined);
    }
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="border-gray-100 border-b">
        <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
          <div>
            <CardTitle className="font-semibold text-lg">
              Nội dung khóa học
            </CardTitle>
            <CardDescription className="mt-1">
              Quản lý các bài học và tài liệu trong khóa học
            </CardDescription>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1 rounded-lg bg-gray-100 p-1">
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="h-8 px-3 shadow-none"
              >
                <List className="mr-1 h-4 w-4" />
                Danh sách
              </Button>
              <Button
                variant={viewMode === "cards" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("cards")}
                className="h-8 px-3 shadow-none"
              >
                <Grid3X3 className="mr-1 h-4 w-4" />
                Thẻ
              </Button>
            </div>
            {canEdit && (
              <Button onClick={handleCreateLesson}>
                <Plus className="mr-2 h-4 w-4" />
                Thêm bài học mới
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="px-6">
        {lessons && lessons.length > 0 && (
          <div className="mb-8 grid grid-cols-1 gap-4 sm:grid-cols-2 xl:grid-cols-4">
            <Card className="@container/card border-blue-100 bg-gradient-to-br from-blue-50 via-blue-25 to-white shadow-sm transition-all duration-200 hover:shadow-md">
              <CardHeader className="pb-3">
                <CardDescription className="font-medium">
                  Tổng bài học
                </CardDescription>
                <div className="flex items-center justify-between">
                  <CardTitle className="font-bold @[250px]/card:text-3xl text-2xl text-blue-700 tabular-nums">
                    {lessons.length}
                  </CardTitle>
                  <BookOpen className="h-8 w-8 text-blue-700 opacity-80" />
                </div>
              </CardHeader>
            </Card>
            <Card className="@container/card border-green-100 bg-gradient-to-br from-green-50 via-green-25 to-white shadow-sm transition-all duration-200 hover:shadow-md">
              <CardHeader className="pb-3">
                <CardDescription className="font-medium">
                  Đã xuất bản
                </CardDescription>
                <div className="flex items-center justify-between">
                  <CardTitle className="font-bold @[250px]/card:text-3xl text-2xl text-green-700 tabular-nums">
                    {lessons.filter((l) => l.published_at).length}
                  </CardTitle>
                  <Eye className="h-8 w-8 text-green-400 opacity-80" />
                </div>
              </CardHeader>
            </Card>
            <Card className="@container/card border-purple-100 bg-gradient-to-br from-purple-50 via-purple-25 to-white shadow-sm transition-all duration-200 hover:shadow-md">
              <CardHeader className="pb-3">
                <CardDescription className="font-medium ">
                  Module
                </CardDescription>
                <div className="flex items-center justify-between">
                  <CardTitle className="font-bold @[250px]/card:text-3xl text-2xl text-purple-700 tabular-nums">
                    {existingGroups.length}
                  </CardTitle>
                  <Grid3X3 className="h-8 w-8 text-purple-400 opacity-80" />
                </div>
              </CardHeader>
            </Card>
            <Card className="@container/card border-orange-100 bg-gradient-to-br from-orange-50 via-orange-25 to-white shadow-sm transition-all duration-200 hover:shadow-md">
              <CardHeader className="pb-3">
                <CardDescription className="font-medium">
                  Tổng thời lượng
                </CardDescription>
                <div className="flex items-center justify-between">
                  <CardTitle className="font-bold @[250px]/card:text-3xl text-2xl text-orange-700 tabular-nums">
                    {lessons.reduce((sum, lesson) => sum + lesson.duration, 0)}
                  </CardTitle>
                  <Timer className="h-8 w-8 text-orange-400 opacity-80" />
                </div>
              </CardHeader>
            </Card>
          </div>
        )}

        {lessons && lessons.length === 0 && (
          <div className="py-16 text-center">
            <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100">
              <BookOpen className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="mb-3 font-semibold text-gray-900 text-xl">
              Chưa có bài học nào
            </h3>
            <p className="mb-8 text-gray-600">
              Bắt đầu tạo bài học đầu tiên cho khóa học của bạn và xây dựng nội
              dung học tập hấp dẫn
            </p>
            <Button onClick={handleCreateLesson} size="lg">
              <Plus className="mr-2 h-5 w-5" />
              Tạo bài học đầu tiên
            </Button>
          </div>
        )}

        {lessons && lessons.length > 0 && (
          <div>
            {viewMode === "list" ? (
              <div className="space-y-8">
                {lessonGroups.map((group) => (
                  <div key={group.groupName} className="space-y-4">
                    <div className="flex items-center gap-3">
                      <h3 className="font-semibold text-gray-900 text-lg">
                        {group.groupName}
                      </h3>
                      <div className="h-px flex-1 bg-gray-200" />
                      <Badge variant="secondary" className="text-xs">
                        {group.lessons.length} bài học
                      </Badge>
                    </div>
                    <div className="space-y-3">
                      {group.lessons.map((lesson) => (
                        <div
                          key={lesson.id}
                          className="group relative flex items-center justify-between rounded-xl border border-gray-200 bg-white p-5 shadow-sm transition-all duration-200 hover:border-gray-300 hover:bg-gray-50 hover:shadow-md"
                        >
                          <div className="flex items-center gap-4">
                            <div className="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 shadow-md transition-all duration-200 group-hover:scale-105 group-hover:shadow-emerald-500/25 group-hover:shadow-lg">
                              <GraduationCap className="h-7 w-7 text-white" />
                            </div>
                            <div className="min-w-0 flex-1">
                              <div className="flex items-start justify-between gap-4">
                                <div>
                                  <h4 className="font-semibold text-gray-900 text-lg transition-colors group-hover:text-blue-600">
                                    {lesson.name}
                                  </h4>
                                  <div className="mt-2 flex items-center gap-6 text-gray-600 text-sm">
                                    <div className="flex items-center gap-1">
                                      <Video className="h-4 w-4" />
                                      <span>{lesson.duration} phút</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <Trophy className="h-4 w-4" />
                                      <span>{lesson.points} điểm</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <FileText className="h-4 w-4" />
                                      <span>
                                        {lesson.sections?.length || 0} phần
                                      </span>
                                    </div>
                                    <Badge
                                      variant="outline"
                                      className="border-blue-200 bg-blue-50 text-blue-700 text-xs"
                                    >
                                      #{lesson.ordinal_index}
                                    </Badge>
                                  </div>
                                  <div className="mt-3">
                                    <ModuleBadge
                                      moduleName={lesson.group}
                                      onAddToModule={() =>
                                        handleEditLesson(lesson)
                                      }
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <LessonStatusBadge
                              publishedAt={lesson.published_at}
                              archivedAt={lesson.archived_at}
                              className="shadow-sm"
                            />
                            {canEdit && (
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="border-gray-200 bg-white shadow-sm transition-all duration-200 hover:border-blue-200 hover:bg-blue-50 hover:text-blue-700 hover:shadow-md"
                                  onClick={() => handleEditLesson(lesson)}
                                >
                                  <Edit className="mr-1 h-4 w-4" />
                                  Chỉnh sửa
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="border-emerald-200 bg-emerald-50 text-emerald-700 shadow-sm transition-all duration-200 hover:border-emerald-300 hover:bg-emerald-100 hover:shadow-md"
                                  onClick={() => handleCreateSlide(lesson)}
                                >
                                  <Presentation className="mr-1 h-4 w-4" />
                                  Tạo slide
                                </Button>
                                {/* <Button
                                variant="outline"
                                size="sm"
                                className="border-red-200 bg-red-50 text-red-700 shadow-sm transition-all duration-200 hover:border-red-300 hover:bg-red-100 hover:shadow-md"
                                onClick={() => handleDeleteLesson(lesson)}
                              >
                                <Trash2 className="mr-1 h-4 w-4" />
                                Xóa
                              </Button> */}
                                {/* <LessonQuickActions
                                lesson={lesson}
                                onEdit={() => handleEditLesson(lesson)}
                              /> */}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-8">
                {lessonGroups.map((group) => (
                  <div key={group.groupName} className="space-y-4">
                    <div className="flex items-center gap-3">
                      <h3 className="font-semibold text-gray-900 text-lg">
                        {group.groupName}
                      </h3>
                      <div className="h-px flex-1 bg-gray-200" />
                      <Badge variant="secondary" className="text-xs">
                        {group.lessons.length} bài học
                      </Badge>
                    </div>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
                      {group.lessons.map((lesson) => (
                        <Card
                          key={lesson.id}
                          className="group hover:-translate-y-1 border-gray-200 bg-white shadow-sm transition-all duration-200 hover:border-gray-300 hover:shadow-lg"
                        >
                          <CardHeader className="pb-3">
                            <div className="mb-3 flex items-center justify-between">
                              <Badge
                                variant="outline"
                                className="border-blue-200 bg-blue-50 text-blue-700 text-xs shadow-sm"
                              >
                                #{lesson.ordinal_index}
                              </Badge>
                              <LessonStatusBadge
                                publishedAt={lesson.published_at}
                                archivedAt={lesson.archived_at}
                                className="text-xs shadow-sm"
                              />
                            </div>
                            <div className="flex items-start gap-3">
                              <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 shadow-md transition-all duration-200 group-hover:scale-110 group-hover:shadow-emerald-500/25 group-hover:shadow-lg">
                                <GraduationCap className="h-6 w-6 text-white" />
                              </div>
                              <div className="min-w-0 flex-1">
                                <CardTitle className="line-clamp-2 text-base leading-snug transition-colors group-hover:text-blue-600">
                                  {lesson.name}
                                </CardTitle>
                                <p className="wrap-break-word mt-2 font-mono text-gray-500 text-xs">
                                  {lesson.slug}
                                </p>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <div className="space-y-4">
                              {/* Module Badge */}
                              <ModuleBadge
                                moduleName={lesson.group}
                                onAddToModule={
                                  canEdit
                                    ? () => handleEditLesson(lesson)
                                    : undefined
                                }
                                className="justify-start"
                              />

                              {/* Stats */}
                              <div className="grid grid-cols-2 gap-3 rounded-lg bg-gray-50 p-3">
                                <div className="flex items-center gap-2">
                                  <div className="flex h-6 w-6 items-center justify-center rounded bg-blue-100">
                                    <FileText className="h-3 w-3 text-blue-600" />
                                  </div>
                                  <span className="font-medium text-gray-900 text-sm">
                                    {lesson.sections?.length || 0} phần
                                  </span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <div className="flex h-6 w-6 items-center justify-center rounded bg-orange-100">
                                    <Trophy className="h-3 w-3 text-orange-600" />
                                  </div>
                                  <span className="font-medium text-gray-900 text-sm">
                                    {lesson.points} điểm
                                  </span>
                                </div>
                              </div>

                              <div className="flex items-center gap-2 text-sm">
                                <Video className="h-4 w-4 text-gray-500" />
                                <span className="text-gray-600">
                                  {lesson.duration} phút học
                                </span>
                              </div>

                              {/* Action Buttons */}
                              {canEdit && (
                                <div className="space-y-2 pt-2">
                                  <div className="flex items-center gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="flex-1 border-gray-200 bg-white text-gray-700 shadow-sm transition-all duration-200 hover:border-blue-200 hover:bg-blue-50 hover:text-blue-700 hover:shadow-md"
                                      onClick={() => handleEditLesson(lesson)}
                                    >
                                      <Edit className="mr-1 h-3 w-3" />
                                      Sửa
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="flex-1 border-emerald-200 bg-emerald-50 text-emerald-700 shadow-sm transition-all duration-200 hover:border-emerald-300 hover:bg-emerald-100 hover:shadow-md"
                                      onClick={() => handleCreateSlide(lesson)}
                                    >
                                      <Presentation className="mr-1 h-3 w-3" />
                                      Slide
                                    </Button>
                                    {/* <div>
                                    <LessonQuickActions
                                      lesson={lesson}
                                      onEdit={() => handleEditLesson(lesson)}
                                    />
                                  </div> */}
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {/* <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex-1 border-red-200 bg-red-50 text-red-700 shadow-sm transition-all duration-200 hover:border-red-300 hover:bg-red-100 hover:shadow-md"
                                    onClick={() => handleDeleteLesson(lesson)}
                                  >
                                    <Trash2 className="mr-1 h-3 w-3" />
                                    Xóa
                                  </Button> */}
                                  </div>
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </CardContent>

      {/* Lesson Form Modal */}
      <LessonForm
        open={showLessonForm}
        courseId={course.id}
        lesson={editingLesson}
        existingGroups={existingGroups}
        existingLessons={lessons || []}
        onClose={handleCloseLessonForm}
        onSuccess={(lesson) => {
          // Additional success handling - force refetch to ensure UI is up to date
          refetchLessons();
        }}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmDialog
        open={showDeleteConfirm}
        onOpenChange={setShowDeleteConfirm}
        title="Xác nhận xóa bài học"
        description={
          <div>
            <p>
              Bạn có chắc chắn muốn xóa bài học{" "}
              <strong>"{lessonToDelete?.name}"</strong> không?
            </p>
            <p className="mt-2 text-gray-600 text-sm">
              Hành động này không thể hoàn tác và sẽ xóa tất cả nội dung của bài
              học.
            </p>
          </div>
        }
        confirmText="Xóa bài học"
        cancelText="Hủy"
        variant="destructive"
        onConfirm={confirmDeleteLesson}
      />
    </Card>
  );
};
