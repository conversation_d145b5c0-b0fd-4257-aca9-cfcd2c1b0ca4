import {
  IconBook2,
  IconBuilding,
  IconCamera,
  IconChartAreaLine,
  IconDatabase,
  IconFileAi,
  IconFileDescription,
  IconFileWord,
  IconReport,
  IconSchool,
  IconUsersGroup,
} from "@tabler/icons-react";
import * as React from "react";
import logoUrl from "@/assets/images/aicademy-logo-mobile.png";

import { NavMain } from "@/components/nav-main";
import { NavSecondary } from "@/components/nav-secondary";
import { NavUser } from "@/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { m } from "@/paraglide/messages.js";
import { useOrganization } from "@/services/admin";
import { LanguageToggle } from "./LanguageToggle";
import { PERMISSION } from "./organization";
import { Separator } from "./ui/separator";

const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "",
  },
  navMain: [
    {
      title: m["sidebar.organizationSettings"](),
      url: "/dashboard/organization",
      icon: IconBuilding,
    },
    {
      title: m["sidebar.userManagement"](),
      url: "/dashboard/users",
      icon: IconUsersGroup,
      permission: PERMISSION.USER_MANAGE,
    },
    {
      title: m["sidebar.courseManagement"](),
      url: "/dashboard/courses",
      icon: IconSchool,
      permission: PERMISSION.COURSE_MANAGE,
    },
    {
      title: m["sidebar.wikiManagement"](),
      url: "/dashboard/wiki",
      icon: IconBook2,
      permission: PERMISSION.WIKI_MANAGE,
    },
    {
      title: m["sidebar.analyticsReports"](),
      url: "/dashboard/analytics",
      icon: IconChartAreaLine,
    },
    // {
    //   title: m["sidebar.collaborationLXP"](),
    //   url: "#",
    //   icon: IconUsers,
    // },
  ],
  navClouds: [
    {
      title: "Capture",
      icon: IconCamera,
      isActive: true,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Proposal",
      icon: IconFileDescription,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Prompts",
      icon: IconFileAi,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
  ],
  navSecondary: [
    // {
    //   title: "Settings",
    //   url: "#",
    //   icon: IconSettings,
    // },
    // {
    //   title: "Get Help",
    //   url: "#",
    //   icon: IconHelp,
    // },
    // {
    //   title: "Search",
    //   url: "#",
    //   icon: IconSearch,
    // },
  ],
  documents: [
    {
      name: "Data Library",
      url: "#",
      icon: IconDatabase,
    },
    {
      name: "Reports",
      url: "#",
      icon: IconReport,
    },
    {
      name: "Word Assistant",
      url: "#",
      icon: IconFileWord,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: organization } = useOrganization();

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            {organization && (
              <img
                src={organization?.logo_url ?? logoUrl}
                alt=""
                className="max-w-36"
              />
            )}
          </div>
        </div>
        <Separator />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        {/* <NavDocuments items={data.documents} /> */}
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
