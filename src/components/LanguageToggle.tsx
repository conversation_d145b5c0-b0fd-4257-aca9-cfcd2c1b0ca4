import { Check, ChevronDown, Globe } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { getLocale, locales, setLocale } from "@/paraglide/runtime";

const languageConfig = {
  vi: {
    name: "Tiếng Việt",
    shortName: "VI",
    flag: "🇻🇳",
  },
  en: {
    name: "English",
    shortName: "EN",
    flag: "🇺🇸",
  },
} as const;

// Component for standalone usage
export function LanguageToggle() {
  const [currentLang, setCurrentLang] = useState(getLocale());
  const [isChanging, setIsChanging] = useState(false);

  const handleLanguageChange = async (newLang: "vi" | "en") => {
    if (newLang === currentLang) return;

    setIsChanging(true);
    setCurrentLang(newLang);

    // Small delay to show loading state before reload
    setTimeout(() => {
      setLocale(newLang);
    }, 150);
  };

  const currentConfig = languageConfig[currentLang];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="relative h-auto gap-1.5 rounded-md border border-transparent px-2 py-1.5 transition-colors hover:border-border hover:bg-accent/50"
          disabled={isChanging}
          aria-label="Change language"
        >
          <Globe className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium text-sm">{currentConfig.shortName}</span>
          <ChevronDown className="h-3 w-3 text-muted-foreground" />
          {isChanging && (
            <div className="absolute inset-0 flex items-center justify-center rounded-md bg-background/80">
              <div className="h-3 w-3 animate-spin rounded-full border border-primary border-t-transparent" />
            </div>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[140px]">
        {locales.map((lang) => {
          const config = languageConfig[lang as keyof typeof languageConfig];
          const isActive = lang === currentLang;

          return (
            <DropdownMenuItem
              key={lang}
              onClick={() => handleLanguageChange(lang as "vi" | "en")}
              className="w-full cursor-pointer gap-2 px-3 py-2 focus:bg-accent"
              disabled={isChanging}
            >
              <span className="text-base">{config.flag}</span>
              <span className="min-w-16 flex-1 text-sm">{config.name}</span>
              {isActive && <Check className="h-3 w-3 text-primary" />}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Component for use inside dropdown menus (like nav-user)
export function LanguageToggleSubmenu() {
  const [currentLang, setCurrentLang] = useState(getLocale());
  const [isChanging, setIsChanging] = useState(false);

  const handleLanguageChange = async (newLang: "vi" | "en") => {
    if (newLang === currentLang) return;

    setIsChanging(true);
    setCurrentLang(newLang);

    // Small delay to show loading state before reload
    setTimeout(() => {
      setLocale(newLang);
    }, 150);
  };

  const currentConfig = languageConfig[currentLang];

  return (
    <DropdownMenuSub>
      <DropdownMenuSubTrigger disabled={isChanging}>
        <div className="flex items-center justify-start gap-2 py-1.5">
          <Globe className="h-4 w-4" />
          <span>Language</span>
          <span className="ml-auto text-muted-foreground text-xs">
            {currentConfig.shortName.toUpperCase()}
          </span>
        </div>
      </DropdownMenuSubTrigger>
      <DropdownMenuSubContent>
        {locales.map((lang) => {
          const config = languageConfig[lang as keyof typeof languageConfig];
          const isActive = lang === currentLang;

          return (
            <DropdownMenuItem
              key={lang}
              onClick={() => handleLanguageChange(lang as "vi" | "en")}
              className="cursor-pointer gap-2"
              disabled={isChanging}
            >
              <span className="text-base">{config.flag}</span>
              <span className="flex-1">{config.name}</span>
              {isActive && <Check className="h-4 w-4 text-primary" />}
              {isChanging && lang === currentLang && (
                <div className="h-3 w-3 animate-spin rounded-full border border-primary border-t-transparent" />
              )}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuSubContent>
    </DropdownMenuSub>
  );
}
