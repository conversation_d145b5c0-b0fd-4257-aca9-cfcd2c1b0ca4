import { useForm } from "@tanstack/react-form";
import { useQueryClient } from "@tanstack/react-query";
import { AlertCircle } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  addMembersToDepartment,
  createUser,
  useDepartments,
} from "@/services/admin";
import { Department } from "@/types/organization";

// User form schema for TanStack Form
interface UserFormSchema {
  username: string;
  email: string;
  departmentId: string;
}

interface AddUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AddUserDialog({ open, onOpenChange }: AddUserDialogProps) {
  const { data: departments = [] } = useDepartments();
  const queryClient = useQueryClient();

  // Loading state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [importError, setImportError] = useState<string | null>(null);

  // Initialize TanStack Form
  const form = useForm({
    defaultValues: {
      username: "",
      email: "",
      departmentId: "none",
    } as UserFormSchema,
    onSubmit: async ({ value }) => {
      await handleSubmit(value);
    },
    validators: {
      onSubmit: ({ value }) => {
        const errors: Record<string, string> = {};

        // Validate name fields
        if (!value.username?.trim()) {
          errors.firstName = "Họ là bắt buộc";
        }

        // Validate email
        if (!value.email?.trim()) {
          errors.email = "Email là bắt buộc";
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value.email)) {
          errors.email = "Email không hợp lệ";
        }

        return Object.keys(errors).length > 0 ? errors : undefined;
      },
    },
  });

  // Handle form submission
  const handleSubmit = async (formData: UserFormSchema) => {
    try {
      setIsSubmitting(true);
      setImportError(null);

      // Full name from username
      const name = formData.username.trim();

      // Call API to create user - similar to bulk import but with single user
      const importedUsers = await createUser({
        name,
        email: formData.email,
      });

      // If user created successfully and we have a valid department ID (not "none"), add to department
      if (
        importedUsers &&
        importedUsers.length > 0 &&
        formData.departmentId &&
        formData.departmentId !== "none"
      ) {
        await addMembersToDepartment({
          department_id: formData.departmentId,
          user_ids: [importedUsers[0].id],
        });
      }

      // Update users and departments list data
      queryClient.invalidateQueries({ queryKey: ["admin-users"] });
      queryClient.invalidateQueries({ queryKey: ["admin-departments"] });

      toast.success("Người dùng mới đã được tạo thành công");
      onOpenChange(false);
      form.reset(); // Reset the form
    } catch (error) {
      console.error("Error creating user:", error);
      setImportError("Đã xảy ra lỗi khi tạo người dùng. Vui lòng thử lại.");
      toast.error("Không thể tạo người dùng mới. Vui lòng thử lại.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen && !isSubmitting) {
          form.reset();
        }
        onOpenChange(isOpen);
      }}
    >
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Thêm người dùng mới</DialogTitle>
          <DialogDescription>Tạo tài khoản mới cho nhân viên</DialogDescription>
        </DialogHeader>

        {importError && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Lỗi</AlertTitle>
            <AlertDescription>{importError}</AlertDescription>
          </Alert>
        )}

        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="space-y-4"
        >
          <div className="grid grid-cols-2 gap-4">
            <form.Field
              name="username"
              validators={{
                onChange: ({ value }) =>
                  !value || value.trim().length === 0
                    ? "Tên là bắt buộc"
                    : undefined,
              }}
              children={(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Tên</Label>
                  <Input
                    id={field.name}
                    placeholder="Văn A"
                    value={field.state.value}
                    onChange={(e) => field.handleChange(e.target.value)}
                    className={
                      field.state.meta.errors.length > 0
                        ? "border-destructive"
                        : ""
                    }
                  />
                  {field.state.meta.errors.length > 0 && (
                    <p className="text-destructive text-sm">
                      {field.state.meta.errors[0]}
                    </p>
                  )}
                </div>
              )}
            />
          </div>

          <form.Field
            name="email"
            validators={{
              onChange: ({ value }) => {
                if (!value || value.trim().length === 0) {
                  return "Email là bắt buộc";
                }

                if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                  return "Email không hợp lệ";
                }

                return undefined;
              },
            }}
            children={(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Email</Label>
                <Input
                  id={field.name}
                  type="email"
                  placeholder="<EMAIL>"
                  value={field.state.value}
                  onChange={(e) => field.handleChange(e.target.value)}
                  className={
                    field.state.meta.errors.length > 0
                      ? "border-destructive"
                      : ""
                  }
                />
                {field.state.meta.errors.length > 0 && (
                  <p className="text-destructive text-sm">
                    {field.state.meta.errors[0]}
                  </p>
                )}
              </div>
            )}
          />

          {/* Role selection is not needed since it's handled by departments */}

          <form.Field
            name="departmentId"
            children={(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Phòng ban</Label>
                <Select
                  value={field.state.value}
                  onValueChange={(value) => field.handleChange(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn phòng ban" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Không có phòng ban</SelectItem>
                    {departments.map((department: Department) => (
                      <SelectItem
                        key={department.id}
                        value={department.id.toString()}
                      >
                        {department.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          />

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
              type="button"
            >
              Hủy
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !form.state.canSubmit}
            >
              {isSubmitting ? "Đang xử lý..." : "Tạo tài khoản"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
